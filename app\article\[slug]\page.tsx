import { notFound } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { formatDistanceToNow, format } from 'date-fns'
import { MainLayout } from '@/components/layout/main-layout'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { dbServer } from '@/lib/database'
import { Clock, User, Calendar, Share2 } from 'lucide-react'
import type { Metadata } from 'next'

interface ArticlePageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: ArticlePageProps): Promise<Metadata> {
  try {
    const article = await dbServer.getArticleBySlug(params.slug)
    
    if (!article) {
      return {
        title: 'Article Not Found',
      }
    }

    return {
      title: article.title,
      description: article.excerpt || article.title,
      openGraph: {
        title: article.title,
        description: article.excerpt || article.title,
        images: article.featured_image ? [article.featured_image] : [],
        type: 'article',
        publishedTime: article.published_at || article.created_at,
        authors: [article.author_name || 'Unknown'],
      },
      twitter: {
        card: 'summary_large_image',
        title: article.title,
        description: article.excerpt || article.title,
        images: article.featured_image ? [article.featured_image] : [],
      },
    }
  } catch (error) {
    return {
      title: 'Article Not Found',
    }
  }
}

export default async function ArticlePage({ params }: ArticlePageProps) {
  try {
    const article = await dbServer.getArticleBySlug(params.slug)

    if (!article) {
      notFound()
    }

    const publishedDate = article.published_at || article.created_at
    const imageUrl = article.featured_image || 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=1200&h=600&fit=crop&crop=center'

    return (
      <MainLayout>
        <article className="bg-white">
          {/* Hero Section */}
          <div className="relative h-96 md:h-[500px] overflow-hidden">
            <Image
              src={imageUrl}
              alt={article.title}
              fill
              className="object-cover"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
            
            {/* Article badges */}
            <div className="absolute top-6 left-6 flex gap-2">
              {article.is_breaking && (
                <Badge variant="destructive" className="animate-pulse">
                  BREAKING NEWS
                </Badge>
              )}
              {article.is_featured && (
                <Badge className="bg-blue-600">
                  FEATURED
                </Badge>
              )}
            </div>

            {/* Article title overlay */}
            <div className="absolute bottom-0 left-0 right-0 p-6 md:p-8 text-white">
              <div className="max-w-4xl mx-auto">
                <Link 
                  href={`/category/${article.category_slug}`}
                  className="text-blue-400 text-sm font-medium hover:underline"
                >
                  {article.category_name}
                </Link>
                <h1 className="text-3xl md:text-5xl font-bold mt-2 leading-tight">
                  {article.title}
                </h1>
                {article.excerpt && (
                  <p className="text-xl text-gray-200 mt-4 leading-relaxed">
                    {article.excerpt}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Article Content */}
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Article Meta */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 pb-6 border-b border-gray-200">
              <div className="flex items-center space-x-4 mb-4 md:mb-0">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={article.author_avatar || ''} />
                  <AvatarFallback>
                    {article.author_name?.charAt(0) || 'A'}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-semibold text-gray-900">{article.author_name}</p>
                  <div className="flex items-center text-sm text-gray-500 space-x-4">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      <span>{format(new Date(publishedDate), 'MMMM d, yyyy')}</span>
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      <span>{formatDistanceToNow(new Date(publishedDate), { addSuffix: true })}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Share button placeholder */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">Share:</span>
                <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                  <Share2 className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Article Body */}
            <div 
              className="prose prose-lg max-w-none"
              dangerouslySetInnerHTML={{ __html: article.content }}
            />

            {/* Article Footer */}
            <div className="mt-12 pt-8 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-500">Category:</span>
                  <Link 
                    href={`/category/${article.category_slug}`}
                    className="text-blue-600 hover:underline font-medium"
                  >
                    {article.category_name}
                  </Link>
                </div>
                
                <div className="text-sm text-gray-500">
                  Last updated: {format(new Date(article.updated_at), 'MMMM d, yyyy')}
                </div>
              </div>
            </div>
          </div>
        </article>
      </MainLayout>
    )
  } catch (error) {
    console.error('Error loading article:', error)
    notFound()
  }
}
