import { Suspense } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { FeaturedArticles } from '@/components/home/<USER>'
import { LatestNews } from '@/components/home/<USER>'
import { CategoryNavigation } from '@/components/home/<USER>'
import { NewsletterSignup } from '@/components/home/<USER>'

export default function Home() {
  return (
    <MainLayout>
      <div className="bg-gray-50">
        {/* Hero Section with Featured Articles */}
        <section className="bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <Suspense fallback={<div className="h-96 bg-gray-200 animate-pulse rounded-lg" />}>
              <FeaturedArticles />
            </Suspense>
          </div>
        </section>

        {/* Category Navigation */}
        <section className="bg-blue-600 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <Suspense fallback={<div className="h-12 bg-blue-500 animate-pulse rounded" />}>
              <CategoryNavigation />
            </Suspense>
          </div>
        </section>

        {/* Latest News Grid */}
        <section className="py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-3xl font-bold text-gray-900">Latest News</h2>
              <a
                href="/news"
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                View All →
              </a>
            </div>
            <Suspense fallback={
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-80 bg-gray-200 animate-pulse rounded-lg" />
                ))}
              </div>
            }>
              <LatestNews />
            </Suspense>
          </div>
        </section>

        {/* Newsletter Signup */}
        <section className="bg-gray-900 text-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <NewsletterSignup />
          </div>
        </section>
      </div>
    </MainLayout>
  )
}
