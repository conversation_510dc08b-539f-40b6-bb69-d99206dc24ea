'use client'

import { useState, useEffect } from 'react'
import { DatabaseClient } from '@/lib/database'
import { TraditionalHeroSection } from './traditional-hero-section'
import { TraditionalMainContent } from './traditional-main-content'
import { TraditionalSidebar } from './traditional-sidebar'
import { TraditionalBottomSection } from './traditional-bottom-section'

interface Article {
  id: string
  title: string
  excerpt: string
  image_url?: string
  published_at: string
  category: string
  slug: string
}

interface Category {
  id: string
  name: string
  slug: string
}

export function TraditionalHomepage() {
  const [data, setData] = useState<{
    featuredArticles: Article[]
    latestArticles: Article[]
    breakingNews: Article[]
    categories: Category[]
  } | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchData() {
      try {
        const db = new DatabaseClient()

        // Fetch all the data we need
        const [featuredArticles, latestArticles, breakingNews, categories] = await Promise.all([
          db.getPublishedArticles({ limit: 6, isFeatured: true }),
          db.getPublishedArticles({ limit: 20 }),
          db.getPublishedArticles({ limit: 5, isBreaking: true }),
          db.getCategories()
        ])

        setData({
          featuredArticles,
          latestArticles,
          breakingNews,
          categories
        })
      } catch (err) {
        console.error('Error loading homepage:', err)
        setError('समाचार लोड करने में त्रुटि हुई')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">समाचार लोड हो रहा है...</h2>
          <p className="text-gray-600">कृपया प्रतीक्षा करें</p>
        </div>
      </div>
    )
  }

  if (error || !data) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">त्रुटि</h2>
          <p className="text-gray-600">{error || 'डेटा लोड नहीं हो सका'}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white min-h-screen">
      {/* Hero Section with Main Story */}
      <TraditionalHeroSection
        mainStory={data.featuredArticles[0]}
        featuredStories={data.featuredArticles.slice(1, 4)}
        breakingNews={data.breakingNews}
      />

      {/* Main Content Area */}
      <div className="max-w-7xl mx-auto px-2 py-4">
        <div className="grid grid-cols-12 gap-4">
          {/* Main Content - 8 columns */}
          <div className="col-span-12 lg:col-span-8">
            <TraditionalMainContent
              articles={data.latestArticles}
              categories={data.categories}
            />
          </div>

          {/* Sidebar - 4 columns */}
          <div className="col-span-12 lg:col-span-4">
            <TraditionalSidebar
              trendingArticles={data.latestArticles.slice(0, 8)}
              categories={data.categories}
            />
          </div>
        </div>
      </div>

      {/* Bottom Section */}
      <TraditionalBottomSection
        articles={data.latestArticles.slice(10, 20)}
        categories={data.categories}
      />
    </div>
  )
}
