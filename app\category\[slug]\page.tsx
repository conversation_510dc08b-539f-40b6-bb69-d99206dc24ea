import { notFound } from 'next/navigation'
import { Suspense } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { ArticleCard } from '@/components/articles/article-card'
import { dbServer } from '@/lib/database'
import type { Metadata } from 'next'

interface CategoryPageProps {
  params: {
    slug: string
  }
  searchParams: {
    page?: string
  }
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  try {
    const category = await dbServer.getCategoryBySlug(params.slug)
    
    if (!category) {
      return {
        title: 'Category Not Found',
      }
    }

    return {
      title: `${category.name} News - NewsPortal`,
      description: category.description || `Latest ${category.name.toLowerCase()} news and updates`,
      openGraph: {
        title: `${category.name} News`,
        description: category.description || `Latest ${category.name.toLowerCase()} news and updates`,
        type: 'website',
      },
    }
  } catch (error) {
    return {
      title: 'Category Not Found',
    }
  }
}

async function CategoryArticles({ categorySlug, page = 1 }: { categorySlug: string, page?: number }) {
  const articlesPerPage = 12
  const offset = (page - 1) * articlesPerPage

  try {
    const articles = await dbServer.getPublishedArticles({
      limit: articlesPerPage,
      offset,
      categorySlug
    })

    if (articles.length === 0 && page === 1) {
      return (
        <div className="text-center py-12">
          <p className="text-gray-600 text-lg">No articles found in this category yet.</p>
          <p className="text-gray-500 mt-2">Check back later for new content!</p>
        </div>
      )
    }

    if (articles.length === 0 && page > 1) {
      notFound()
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {articles.map((article) => (
          <ArticleCard
            key={article.id}
            article={article}
            variant="default"
            showAuthor={true}
            showCategory={false}
          />
        ))}
      </div>
    )
  } catch (error) {
    console.error('Error fetching category articles:', error)
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Error loading articles. Please try again later.</p>
      </div>
    )
  }
}

export default async function CategoryPage({ params, searchParams }: CategoryPageProps) {
  const page = parseInt(searchParams.page || '1', 10)

  try {
    const category = await dbServer.getCategoryBySlug(params.slug)

    if (!category) {
      notFound()
    }

    return (
      <MainLayout>
        <div className="bg-gray-50 min-h-screen">
          {/* Category Header */}
          <div className="bg-white border-b">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
              <div className="text-center">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">
                  {category.name}
                </h1>
                {category.description && (
                  <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                    {category.description}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Articles Grid */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-2xl font-bold text-gray-900">
                Latest {category.name} News
              </h2>
              
              {/* Pagination info */}
              {page > 1 && (
                <p className="text-gray-600">
                  Page {page}
                </p>
              )}
            </div>

            <Suspense fallback={
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-80 bg-gray-200 animate-pulse rounded-lg" />
                ))}
              </div>
            }>
              <CategoryArticles categorySlug={params.slug} page={page} />
            </Suspense>

            {/* Simple Pagination */}
            <div className="flex justify-center mt-12 space-x-4">
              {page > 1 && (
                <a
                  href={`/category/${params.slug}?page=${page - 1}`}
                  className="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Previous
                </a>
              )}
              
              <a
                href={`/category/${params.slug}?page=${page + 1}`}
                className="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Next
              </a>
            </div>
          </div>
        </div>
      </MainLayout>
    )
  } catch (error) {
    console.error('Error loading category page:', error)
    notFound()
  }
}
