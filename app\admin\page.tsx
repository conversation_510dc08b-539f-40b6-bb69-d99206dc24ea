'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { usePermissions } from '@/components/auth/protected-route'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { db } from '@/lib/database'
import { toast } from 'react-hot-toast'
import { 
  PenTool, 
  Users, 
  FileText, 
  TrendingUp, 
  Plus,
  Edit,
  Trash2,
  Eye
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface DashboardStats {
  totalArticles: number
  publishedArticles: number
  draftArticles: number
  totalUsers: number
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalArticles: 0,
    publishedArticles: 0,
    draftArticles: 0,
    totalUsers: 0
  })
  const [recentArticles, setRecentArticles] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const { canManageUsers, canCreateArticles, user } = usePermissions()

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch recent articles
        const articles = await db.getPublishedArticles({ limit: 5 })
        setRecentArticles(articles)
        
        // In a real app, you'd have API endpoints for these stats
        setStats({
          totalArticles: articles.length,
          publishedArticles: articles.filter(a => a.is_published).length,
          draftArticles: articles.filter(a => !a.is_published).length,
          totalUsers: 0 // Would come from API
        })
      } catch (error) {
        toast.error('Failed to load dashboard data')
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  const handleDeleteArticle = async (articleId: string) => {
    if (!confirm('Are you sure you want to delete this article?')) return

    try {
      await db.deleteArticle(articleId)
      toast.success('Article deleted successfully')
      // Refresh the list
      const articles = await db.getPublishedArticles({ limit: 5 })
      setRecentArticles(articles)
    } catch (error) {
      toast.error('Failed to delete article')
    }
  }

  return (
    <ProtectedRoute requiredRoles={['admin', 'editor', 'writer']}>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-2">
              Welcome back, {user?.full_name || user?.email}
            </p>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {canCreateArticles() && (
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <Link href="/admin/create">
                  <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Create Article</CardTitle>
                    <PenTool className="h-4 w-4 ml-auto text-blue-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-blue-600">+</div>
                    <p className="text-xs text-muted-foreground">
                      Write a new article
                    </p>
                  </CardContent>
                </Link>
              </Card>
            )}

            {canManageUsers() && (
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <Link href="/admin/users">
                  <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Manage Users</CardTitle>
                    <Users className="h-4 w-4 ml-auto text-green-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">{stats.totalUsers}</div>
                    <p className="text-xs text-muted-foreground">
                      Total users
                    </p>
                  </CardContent>
                </Link>
              </Card>
            )}

            <Card>
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Articles</CardTitle>
                <FileText className="h-4 w-4 ml-auto text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">{stats.totalArticles}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.publishedArticles} published, {stats.draftArticles} drafts
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Articles */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Recent Articles</CardTitle>
                  <CardDescription>
                    Your latest published articles
                  </CardDescription>
                </div>
                {canCreateArticles() && (
                  <Button asChild>
                    <Link href="/admin/create">
                      <Plus className="h-4 w-4 mr-2" />
                      New Article
                    </Link>
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="h-16 bg-gray-200 animate-pulse rounded" />
                  ))}
                </div>
              ) : recentArticles.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No articles yet</p>
                  {canCreateArticles() && (
                    <Button asChild className="mt-4">
                      <Link href="/admin/create">Create your first article</Link>
                    </Button>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {recentArticles.map((article) => (
                    <div key={article.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="font-semibold text-gray-900">{article.title}</h3>
                          {article.is_featured && (
                            <Badge className="bg-blue-600">Featured</Badge>
                          )}
                          {article.is_breaking && (
                            <Badge variant="destructive">Breaking</Badge>
                          )}
                        </div>
                        <div className="flex items-center text-sm text-gray-500 space-x-4">
                          <span>{article.category_name}</span>
                          <span>{formatDistanceToNow(new Date(article.created_at), { addSuffix: true })}</span>
                          <span>By {article.author_name}</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/article/${article.slug}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/admin/edit/${article.id}`}>
                            <Edit className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleDeleteArticle(article.id)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </ProtectedRoute>
  )
}
