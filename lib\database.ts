import { createSupabaseClient } from './supabase'
import { createSupabaseServerClient } from './supabase-server'
import { Article, Category, User, CreateArticleData, UpdateArticleData, CreateCategoryData, UpdateCategoryData } from './types'

// Client-side database operations
export class DatabaseClient {
  private supabase = createSupabaseClient()

  // Article operations
  async getPublishedArticles(options: {
    limit?: number
    offset?: number
    categorySlug?: string
    isFeatured?: boolean
    isBreaking?: boolean
  } = {}) {
    const { limit = 10, offset = 0, categorySlug, isFeatured, isBreaking } = options
    
    const { data, error } = await this.supabase.rpc('get_published_articles', {
      limit_count: limit,
      offset_count: offset,
      category_slug: categorySlug || null,
      is_featured_only: isFeatured || false,
      is_breaking_only: isBreaking || false
    })

    if (error) throw error
    return data
  }

  async getArticleBySlug(slug: string) {
    const { data, error } = await this.supabase.rpc('get_article_by_slug', {
      article_slug: slug
    })

    if (error) throw error
    return data?.[0] || null
  }

  async createArticle(articleData: CreateArticleData) {
    const slug = this.generateSlug(articleData.title)
    
    const { data, error } = await this.supabase
      .from('articles')
      .insert({
        ...articleData,
        slug,
        published_at: articleData.is_published ? new Date().toISOString() : null
      })
      .select()
      .single()

    if (error) throw error
    return data
  }

  async updateArticle(articleData: UpdateArticleData) {
    const updateData: any = { ...articleData }
    delete updateData.id

    if (updateData.title) {
      updateData.slug = this.generateSlug(updateData.title)
    }

    if (updateData.is_published && !updateData.published_at) {
      updateData.published_at = new Date().toISOString()
    }

    const { data, error } = await this.supabase
      .from('articles')
      .update(updateData)
      .eq('id', articleData.id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async deleteArticle(id: string) {
    const { error } = await this.supabase
      .from('articles')
      .delete()
      .eq('id', id)

    if (error) throw error
  }

  // Category operations
  async getCategories() {
    const { data, error } = await this.supabase
      .from('categories')
      .select('*')
      .order('name')

    if (error) throw error
    return data
  }

  async getCategoryBySlug(slug: string) {
    const { data, error } = await this.supabase
      .from('categories')
      .select('*')
      .eq('slug', slug)
      .single()

    if (error) throw error
    return data
  }

  async createCategory(categoryData: CreateCategoryData) {
    const { data, error } = await this.supabase
      .from('categories')
      .insert(categoryData)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async updateCategory(categoryData: UpdateCategoryData) {
    const updateData = { ...categoryData }
    delete updateData.id

    const { data, error } = await this.supabase
      .from('categories')
      .update(updateData)
      .eq('id', categoryData.id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async deleteCategory(id: string) {
    const { error } = await this.supabase
      .from('categories')
      .delete()
      .eq('id', id)

    if (error) throw error
  }

  // User operations
  async getUsers() {
    const { data, error } = await this.supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  }

  async updateUserRole(userId: string, role: string) {
    const { data, error } = await this.supabase
      .from('profiles')
      .update({ role })
      .eq('id', userId)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Newsletter operations
  async subscribeToNewsletter(email: string) {
    const { data, error } = await this.supabase
      .from('newsletter_subscribers')
      .insert({ email })
      .select()
      .single()

    if (error) throw error
    return data
  }

  async getNewsletterSubscribers() {
    const { data, error } = await this.supabase
      .from('newsletter_subscribers')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  }

  // File upload operations
  async uploadImage(file: File, folder: string = 'articles') {
    const fileExt = file.name.split('.').pop()
    const fileName = `${Math.random().toString(36).substring(2)}.${fileExt}`
    const filePath = `${folder}/${fileName}`

    const { data, error } = await this.supabase.storage
      .from('article-images')
      .upload(filePath, file)

    if (error) throw error

    const { data: { publicUrl } } = this.supabase.storage
      .from('article-images')
      .getPublicUrl(filePath)

    return publicUrl
  }

  // Utility functions
  private generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }
}

// Server-side database operations
export class DatabaseServer {
  private supabase = createSupabaseServerClient()

  async getPublishedArticles(options: {
    limit?: number
    offset?: number
    categorySlug?: string
    isFeatured?: boolean
    isBreaking?: boolean
  } = {}) {
    const { limit = 10, offset = 0, categorySlug, isFeatured, isBreaking } = options
    
    const { data, error } = await this.supabase.rpc('get_published_articles', {
      limit_count: limit,
      offset_count: offset,
      category_slug: categorySlug || null,
      is_featured_only: isFeatured || false,
      is_breaking_only: isBreaking || false
    })

    if (error) throw error
    return data
  }

  async getArticleBySlug(slug: string) {
    const { data, error } = await this.supabase.rpc('get_article_by_slug', {
      article_slug: slug
    })

    if (error) throw error
    return data?.[0] || null
  }

  async getCategories() {
    const { data, error } = await this.supabase
      .from('categories')
      .select('*')
      .order('name')

    if (error) throw error
    return data
  }
}

// Export instances
export const db = new DatabaseClient()
export const dbServer = new DatabaseServer()
