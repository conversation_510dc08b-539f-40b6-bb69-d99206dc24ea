import Link from 'next/link'
import { dbServer } from '@/lib/database'
import { But<PERSON> } from '@/components/ui/button'

export async function CategoryNavigation() {
  try {
    const categories = await dbServer.getCategories()

    if (categories.length === 0) {
      return null
    }

    return (
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-1 overflow-x-auto">
          <span className="text-sm font-medium whitespace-nowrap mr-4">Browse by Category:</span>
          {categories.map((category) => (
            <Button
              key={category.id}
              variant="ghost"
              size="sm"
              asChild
              className="text-white hover:bg-blue-700 whitespace-nowrap"
            >
              <Link href={`/category/${category.slug}`}>
                {category.name}
              </Link>
            </Button>
          ))}
        </div>
      </div>
    )
  } catch (error) {
    console.error('Error fetching categories:', error)
    return null
  }
}
