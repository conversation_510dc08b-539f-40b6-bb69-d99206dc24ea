import Image from 'next/image'
import Link from 'next/link'
import { formatDistanceToNow } from 'date-fns'
import { hi } from 'date-fns/locale'

interface Article {
  id: string
  title: string
  excerpt: string
  image_url?: string
  published_at: string
  category: string
  slug: string
}

interface Category {
  id: string
  name: string
  slug: string
}

interface TraditionalMainContentProps {
  articles: Article[]
  categories: Category[]
}

export function TraditionalMainContent({ articles, categories }: TraditionalMainContentProps) {
  // Group articles by category
  const articlesByCategory = categories.reduce((acc, category) => {
    acc[category.slug] = articles.filter(article => 
      article.category.toLowerCase() === category.name.toLowerCase()
    ).slice(0, 4)
    return acc
  }, {} as Record<string, Article[]>)

  // Get general articles not in specific categories
  const generalArticles = articles.slice(0, 8)

  return (
    <div className="space-y-8">
      {/* Latest News Section */}
      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-900 border-b-4 border-red-600 pb-2">
            ताज़ा समाचार
          </h2>
          <Link href="/news" className="text-red-600 hover:text-red-800 font-medium">
            सभी समाचार →
          </Link>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {generalArticles.map((article, index) => (
            <Link key={article.id} href={`/news/${article.slug}`}>
              <div className="flex gap-4 group cursor-pointer border-b border-gray-200 pb-4">
                <div className="flex-shrink-0 w-32 h-24 relative overflow-hidden rounded">
                  {article.image_url ? (
                    <Image
                      src={article.image_url}
                      alt={article.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                      <span className="text-xs text-gray-500">IMG</span>
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="inline-block bg-blue-600 text-white px-2 py-1 text-xs font-bold mb-2 rounded">
                    {article.category}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 line-clamp-2 group-hover:text-red-600 transition-colors mb-2">
                    {article.title}
                  </h3>
                  <p className="text-gray-600 text-sm line-clamp-2 mb-2">
                    {article.excerpt}
                  </p>
                  <p className="text-gray-500 text-xs">
                    {formatDistanceToNow(new Date(article.published_at), { 
                      addSuffix: true, 
                      locale: hi 
                    })}
                  </p>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </section>

      {/* Category-wise News Sections */}
      {categories.map((category) => {
        const categoryArticles = articlesByCategory[category.slug]
        if (!categoryArticles || categoryArticles.length === 0) return null

        return (
          <section key={category.id}>
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-gray-900 border-b-4 border-blue-600 pb-2">
                {category.name}
              </h2>
              <Link 
                href={`/category/${category.slug}`} 
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                और देखें →
              </Link>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {categoryArticles.map((article, index) => (
                <Link key={article.id} href={`/news/${article.slug}`}>
                  <div className="flex gap-3 group cursor-pointer border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow">
                    <div className="flex-shrink-0 w-20 h-16 relative overflow-hidden rounded">
                      {article.image_url ? (
                        <Image
                          src={article.image_url}
                          alt={article.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <span className="text-xs text-gray-500">IMG</span>
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-semibold text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors mb-1">
                        {article.title}
                      </h3>
                      <p className="text-xs text-gray-500">
                        {formatDistanceToNow(new Date(article.published_at), { 
                          addSuffix: true, 
                          locale: hi 
                        })}
                      </p>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </section>
        )
      })}

      {/* Video Section Placeholder */}
      <section>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900 border-b-4 border-green-600 pb-2">
            वीडियो न्यूज़
          </h2>
          <Link href="/videos" className="text-green-600 hover:text-green-800 font-medium">
            सभी वीडियो →
          </Link>
        </div>
        
        <div className="bg-gray-100 rounded-lg p-8 text-center">
          <div className="text-gray-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-700 mb-2">वीडियो न्यूज़ जल्द आ रहा है</h3>
          <p className="text-gray-600">हमारे वीडियो न्यूज़ सेक्शन के लिए तैयार रहें</p>
        </div>
      </section>
    </div>
  )
}
